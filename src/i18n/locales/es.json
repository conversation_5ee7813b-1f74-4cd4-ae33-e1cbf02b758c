{"app": {"title": "Alerta CoMapeo", "mapAlertSystem": "Sistema de Alertas de Mapa"}, "auth": {"title": "Sistema de Alertas de Mapa", "subtitle": "Ingresa las credenciales de tu servidor para continuar", "serverName": "Nombre del Servidor", "serverNamePlaceholder": "Ingresa la URL del servidor", "bearerToken": "<PERSON><PERSON>", "bearerTokenPlaceholder": "Ingresa tu token Bearer", "rememberMe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "connect": "Conectar", "connecting": "Conectando...", "successfullyAuthenticated": "Autenticado con éxito", "loggedOutSuccessfully": "Sesión cerrada con éxito"}, "map": {"searchPlaceholder": "Buscar una ubicación...", "recentSearches": "Busquedas recientes:", "selectedLocation": "Ubicación Seleccionada", "continue": "<PERSON><PERSON><PERSON><PERSON>", "manualEntry": "Entrada Manual", "tapToSelect": "Toca en cualquier parte del mapa para seleccionar coordenadas", "locationSelected": "Ubicación seleccionada: {{lat}}, {{lng}}", "locationFound": "Se encontró {{query}}: {{lat}}, {{lng}}", "locationNotFound": "Ubicación no encontrada. Por favor, intenta con un término diferente.", "searchFailed": "Fallo en la búsqueda. Por favor, revisa tu conexión e intenta de nuevo.", "mapConfigError": "Error en la configuración del mapa. Por favor, contacta soporte.", "loadingMap": "Cargando mapa...", "coordinatesCopied": "Coordenadas copiadas al portapapeles", "failedToCopy": "Error al copiar las coordenadas", "coordinatesSetManually": "Coordenadas configuradas manualmente: {{lat}}, {{lng}}", "pleaseSelectCoordinates": "Por favor, selecciona las coordenadas primero"}, "mapbox": {"title": "Configuración de Mapbox", "token": "Token Público de Mapbox", "tokenPlaceholder": "pk.********************************...", "getTokenFrom": "Obtén tu token desde", "initializeMap": "<PERSON><PERSON><PERSON><PERSON>", "backToLogin": "Regresar al inicio de sesión", "tokenSetSuccessfully": "Token de Mapbox configurado con éxito", "enterValidToken": "Por favor, ingresa un token válido de Mapbox"}, "manualCoords": {"title": "Coordenadas Manuales", "latitude": "Latitud (-90 a 90)", "longitude": "<PERSON><PERSON><PERSON> (-180 a 180)", "latitudePlaceholder": "51.5074", "longitudePlaceholder": "-0.1278", "setCoordinates": "<PERSON><PERSON><PERSON>", "invalidCoordinates": "Por favor, ingresa coordenadas válidas (lat: -90 a 90, lng: -180 a 180)"}, "projects": {"title": "Seleccionar Proyectos", "subtitle": "Elige a qué proyectos enviar la alerta", "loadingProjects": "Cargando proyectos...", "noProjectsTitle": "No hay proyectos disponibles", "noProjectsMessage": "No hay proyectos disponibles para tu cuenta. Por favor, contacta a tu administrador para acceder.", "backToMap": "Regresar al Mapa", "foundProjects": "Se encontraron {{count}} proyectos", "failedToFetch": "Error al obtener los proyectos", "pleaseSelectAtLeast": "Por favor, selecciona al menos un proyecto", "continueToAlert": "Continuar al formulario de alerta ({{count}} seleccionados)", "selected": "Proyectos seleccionados {{count}}:", "logout": "<PERSON><PERSON><PERSON>", "selectProject": "Seleccionar Proyecto", "noProjects": "No hay proyectos"}, "alert": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "Detalles de la alerta", "location": "Ubicación:", "selectedProjects": "Proyectos seleccionados ({{count}}):", "detectionStartTime": "Hora de inicio de detección", "detectionEndTime": "Hora de fin de detección", "sourceId": "ID de la fuente", "sourceIdPlaceholder": "Ingresa identificador de fuente", "alertName": "Nombre de la alerta (formato slug)", "alertNamePlaceholder": "nombre-alerta", "slugFormatHelp": "Usa solo letras minúsculas, números y guiones", "invalidFormat": "Formato inválido. Usa solo letras minúsculas, números y guiones", "endTimeAfterStart": "La hora de fin debe ser posterior a la hora de inicio", "fillAllFields": "Por favor, llena todos los campos", "slugFormatError": "El nombre de la alerta debe estar en formato slug (solo letras minúsculas, números y guiones)", "submitAlert": "Enviar alerta a {{count}} proyecto(s)", "creatingAlerts": "<PERSON><PERSON><PERSON> alertas...", "alertCreatedSuccessfully": "Alerta creada con éxito", "partiallyCompleted": "Parcialmente completado", "creationFailed": "Error al crear", "tryAgain": "Intenta de nuevo", "successMessage": "Alerta creada con éxito para {{count}} proyecto(s)", "partialMessage": "Alerta creada para {{successCount}} proyecto(s), falló en {{errorCount}}", "failedMessage": "No se pudo crear la alerta para ningún proyecto", "unexpectedError": "Ocurrió un error inesperado", "copy": "Copiar", "back": "Regresar"}, "alertPopup": {"title": "Detalles de la alerta", "close": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> de la alerta", "alertName": "Nombre de la alerta", "project": "Proyecto", "coordinates": "<PERSON><PERSON><PERSON><PERSON>", "detectionPeriod": "Período de detección", "start": "Inicio: {{date}}", "end": "Fin: {{date}}", "sourceId": "ID de la fuente", "na": "N/A", "invalidDate": "<PERSON><PERSON>"}, "common": {"install": "Instalar", "installApp": "Instalar app", "go": "<PERSON>r", "and": "y", "more": "más"}, "language": {"switchLanguage": "Cambiar idioma", "english": "Inglés"}}