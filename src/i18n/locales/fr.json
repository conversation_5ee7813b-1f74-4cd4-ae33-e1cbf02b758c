{"app": {"title": "Alerte CoMapeo", "mapAlertSystem": "Système d'alerte cartographique"}, "auth": {"title": "Système d'alerte cartographique", "subtitle": "Entrez vos identifiants serveur pour continuer", "serverName": "Nom du serveur", "serverNamePlaceholder": "Entrez l'URL du serveur", "bearerToken": "<PERSON><PERSON>", "bearerTokenPlaceholder": "Entrez votre jeton <PERSON>", "rememberMe": "Souviens-toi de moi", "connect": "Se connecter", "connecting": "Connexion en cours...", "successfullyAuthenticated": "Authentifié avec succès", "loggedOutSuccessfully": "Déconnecté avec succès"}, "map": {"searchPlaceholder": "Rechercher un lieu...", "recentSearches": "Recherches récentes :", "selectedLocation": "<PERSON><PERSON>", "continue": "<PERSON><PERSON><PERSON>", "manualEntry": "<PERSON><PERSON>", "tapToSelect": "Appuyez n'importe où sur la carte pour sélectionner les coordonnées", "locationSelected": "Lieu sélectionné : {{lat}}, {{lng}}", "locationFound": "Trouvé {{query}} : {{lat}}, {{lng}}", "locationNotFound": "Lieu non trouvé. Veuillez essayer un terme de recherche différent.", "searchFailed": "La recherche a échoué. Veuillez vérifier votre connexion et réessayer.", "mapConfigError": "Erreur de configuration de la carte. Veuillez contacter le support.", "loadingMap": "Chargement de la carte...", "coordinatesCopied": "Coordonnées copiées dans le presse-papiers", "failedToCopy": "Échec de la copie des coordonnées", "coordinatesSetManually": "Coordonnées définies manuellement : {{lat}}, {{lng}}", "pleaseSelectCoordinates": "Veuillez d'abord sélectionner des coordonnées"}, "mapbox": {"title": "Configuration Mapbox", "token": "Jeton public Mapbox", "tokenPlaceholder": "pk.********************************...", "getTokenFrom": "Obtenez votre jeton de", "initializeMap": "Initialiser la carte", "backToLogin": "Retour à la connexion", "tokenSetSuccessfully": "Jeton Mapbox configuré avec succès", "enterValidToken": "Veuillez entrer un jeton Mapbox valide"}, "manualCoords": {"title": "Coordonnées manuelles", "latitude": "Latitude (-90 à 90)", "longitude": "Longitude (-180 à 180)", "latitudePlaceholder": "51.5074", "longitudePlaceholder": "-0.1278", "setCoordinates": "Définir les coordonnées", "invalidCoordinates": "Veuillez entrer des coordonnées valides (lat : -90 à 90, lng : -180 à 180)"}, "projects": {"title": "Sélectionner des projets", "subtitle": "Choisissez à quels projets envoyer l'alerte", "loadingProjects": "Chargement des projets...", "noProjectsTitle": "Aucun projet disponible", "noProjectsMessage": "Aucun projet disponible pour votre compte. Veuillez contacter votre administrateur pour accéder.", "backToMap": "Retour à la carte", "foundProjects": "Trouvé {{count}} projets", "failedToFetch": "Échec de la récupération des projets", "pleaseSelectAtLeast": "Veuillez sélectionner au moins un projet", "continueToAlert": "Continuer vers le formulaire d'alerte ({{count}} sélectionné{{plural}})", "selected": "{{count}} projet{{plural}} sélectionné{{plural}} :", "logout": "Se déconnecter", "selectProject": "Sélectionner le projet", "noProjects": "Aucun projet"}, "alert": {"title": "<PERSON><PERSON><PERSON> une alerte", "subtitle": "Dé<PERSON> de l'alerte", "location": "Emplacement :", "selectedProjects": "Projets sélectionnés ({{count}}) :", "detectionStartTime": "Heure de début de détection", "detectionEndTime": "Heure de fin de détection", "sourceId": "ID de la source", "sourceIdPlaceholder": "Entrez l'identifiant de la source", "alertName": "Nom de l'alerte (format slug)", "alertNamePlaceholder": "nom-de-l-alerte", "slugFormatHelp": "Utilisez uniquement des lettres minuscules, des chiffres et des tirets", "invalidFormat": "Format invalide. Utilisez uniquement des lettres minuscules, des chiffres et des tirets", "endTimeAfterStart": "L'heure de fin doit être après l'heure de début", "fillAllFields": "<PERSON><PERSON><PERSON>z remplir tous les champs", "slugFormatError": "Le nom de l'alerte doit être au format slug (minuscules, chiffres et tirets uniquement)", "submitAlert": "Envoyer l'alerte à {{count}} projet{{plural}}", "creatingAlerts": "Création des alertes en cours...", "alertCreatedSuccessfully": "Alerte c<PERSON>ée avec succès", "partiallyCompleted": "Partiellement terminé", "creationFailed": "Échec de la création", "tryAgain": "<PERSON><PERSON><PERSON><PERSON>", "successMessage": "Alerte créée avec succès pour {{count}} projet{{plural}}", "partialMessage": "Alerte créée pour {{successCount}} projet{{successPlural}}, échouée pour {{errorCount}}", "failedMessage": "Échec de la création d'une alerte pour tout projet", "unexpectedError": "Une erreur inattendue est survenue", "copy": "<PERSON><PERSON><PERSON>", "back": "Retour"}, "alertPopup": {"title": "Dé<PERSON> de l'alerte", "close": "<PERSON><PERSON><PERSON> les détails de l'alerte", "alertName": "Nom de l'alerte", "project": "Projet", "coordinates": "Coordonnées", "detectionPeriod": "Période de détection", "start": "Début : {{date}}", "end": "Fin : {{date}}", "sourceId": "ID de la source", "na": "N/A", "invalidDate": "Date invalide"}, "common": {"install": "Installer", "installApp": "Installer l'application", "go": "<PERSON><PERSON>", "and": "et", "more": "plus"}, "language": {"switchLanguage": "Changer de langue", "english": "<PERSON><PERSON><PERSON>"}}