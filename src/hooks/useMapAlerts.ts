
import { useState, useEffect, useRef } from 'react';
import { toast } from 'sonner';
import { apiService } from '@/services/apiService';
import { useTranslation } from 'react-i18next';
import mapboxgl from 'mapbox-gl';

export interface MapAlert {
  id: string;
  name: string;
  coordinates: [number, number];
  projectName: string;
  detectionDateStart: string;
  detectionDateEnd: string;
  sourceId: string;
}

export const useMapAlerts = (
  credentials: any,
  selectedProject: any | null,
  mapInstanceRef: React.MutableRefObject<mapboxgl.Map | null>
) => {
  const { t } = useTranslation();
  const [alerts, setAlerts] = useState<MapAlert[]>([]);
  const [selectedAlert, setSelectedAlert] = useState<MapAlert | null>(null);
  const [isLoadingAlerts, setIsLoadingAlerts] = useState(false);
  const alertMarkersRef = useRef<mapboxgl.Marker[]>([]);

  const loadAlerts = async () => {
    if (!credentials || !selectedProject) return;

    setIsLoadingAlerts(true);
    try {
      // Fetch alerts for the selected project only
      const fetchedAlerts = await apiService.fetchAlerts(credentials, [selectedProject]);
      setAlerts(fetchedAlerts);

      if (mapInstanceRef.current) {
        addAlertMarkers(fetchedAlerts);
      }

      if (fetchedAlerts.length === 0) {
        console.log(`No alerts found for project: ${selectedProject.name}`);
      } else {
        console.log(`Loaded ${fetchedAlerts.length} alerts for project: ${selectedProject.name}`);
      }
    } catch (error) {
      console.error('Error loading alerts:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred while loading alerts';
      toast.error(`Failed to load alerts: ${errorMessage}`);
      setAlerts([]);
    } finally {
      setIsLoadingAlerts(false);
    }
  };

  const addAlertMarkers = (alertList: MapAlert[]) => {
    const mapInstance = mapInstanceRef.current;
    if (!mapInstance) {
      console.warn('Map instance not available when trying to add alert markers');
      return;
    }

    // Clear existing alert markers
    alertMarkersRef.current.forEach(marker => marker.remove());
    alertMarkersRef.current = [];

    // Add new alert markers
    alertList.forEach(alert => {
      const el = document.createElement('div');
      el.className = 'alert-marker';
      el.style.cssText = `
        background-color: #ef4444;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        border: 2px solid white;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        position: relative;
      `;

      // Add label
      const label = document.createElement('div');
      label.textContent = alert.name;
      label.style.cssText = `
        position: absolute;
        top: -30px;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(0,0,0,0.8);
        color: white;
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 12px;
        white-space: nowrap;
        pointer-events: none;
      `;
      el.appendChild(label);

      el.addEventListener('click', () => {
        setSelectedAlert(alert);

        // Haptic feedback
        if ('vibrate' in navigator) {
          navigator.vibrate(50);
        }
      });

      const marker = new mapboxgl.Marker(el)
        .setLngLat(alert.coordinates)
        .addTo(mapInstance);

      alertMarkersRef.current.push(marker);
    });
  };

  // Clean up markers when component unmounts
  const cleanupMarkers = () => {
    alertMarkersRef.current.forEach(marker => marker.remove());
    alertMarkersRef.current = [];
  };

  return {
    alerts,
    selectedAlert,
    setSelectedAlert,
    isLoadingAlerts,
    loadAlerts,
    addAlertMarkers,
    cleanupMarkers
  };
};
