
import axios, { AxiosInstance, AxiosError } from 'axios';

interface Credentials {
  serverName: string;
  bearerToken: string;
}

interface Project {
  projectId: string;
  name: string;
}

interface AlertData {
  detectionDateStart: string;
  detectionDateEnd: string;
  sourceId: string;
  metadata: {
    alert_type: string;
  };
  geometry: {
    type: "Point";
    coordinates: [number, number];
  };
}

interface MapAlert {
  id: string;
  name: string;
  coordinates: [number, number];
  projectName: string;
  detectionDateStart: string;
  detectionDateEnd: string;
  sourceId: string;
}

class ApiService {
  private getApiClient(credentials: Credentials): AxiosInstance {
    const baseURL = this.getBaseUrl(credentials.serverName);

    return axios.create({
      baseURL,
      headers: {
        'Authorization': `Bearer ${credentials.bearerToken}`,
        'Content-Type': 'application/json',
      },
      // Add timeout and better error handling
      timeout: 30000,
      validateStatus: (status) => status < 500, // Don't throw for 4xx errors
    });
  }

  private getBaseUrl(serverName: string): string {
    // In development, use the proxy to avoid CORS issues
    if (import.meta.env.DEV) {
      return '/api';
    }

    // In production, use the full server URL
    if (!serverName.startsWith('http://') && !serverName.startsWith('https://')) {
      return `https://${serverName}`;
    }
    return serverName;
  }

  private handleError(error: unknown, context: string): never {
    if (error instanceof AxiosError) {
      if (error.response) {
        // Server responded with error status
        const status = error.response.status;
        const message = error.response.data?.message || error.response.statusText || error.message;
        throw new Error(`${context}: ${status} ${message}`);
      } else if (error.request) {
        // Request was made but no response received (network error)
        throw new Error(`${context}: Network error - ${error.message}`);
      } else {
        // Something else happened
        throw new Error(`${context}: Request setup error - ${error.message}`);
      }
    } else {
      throw new Error(`${context}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async fetchProjects(credentials: Credentials): Promise<Project[]> {
    try {
      const apiClient = this.getApiClient(credentials);
      const response = await apiClient.get('/projects');

      // Check if response was successful
      if (response.status >= 400) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = response.data;

      // Handle different possible response formats
      if (Array.isArray(data)) {
        return data.map((project, index) => ({
          projectId: project.id || project.projectId || `project-${index}`,
          name: project.name || project.title || `Project ${index + 1}`
        }));
      } else if (data.projects && Array.isArray(data.projects)) {
        return data.projects.map((project: any, index: number) => ({
          projectId: project.id || project.projectId || `project-${index}`,
          name: project.name || project.title || `Project ${index + 1}`
        }));
      } else {
        throw new Error('Invalid response format: expected array of projects or object with projects property');
      }
    } catch (error) {
      console.error('Error fetching projects:', error);
      this.handleError(error, 'Failed to fetch projects');
    }
  }

  async createAlert(credentials: Credentials, projectId: string, alertData: AlertData): Promise<void> {
    try {
      const apiClient = this.getApiClient(credentials);
      await apiClient.post(`/projects/${projectId}/remoteDetectionAlerts`, alertData);

      console.log(`Alert created successfully for project ${projectId}:`, alertData);
    } catch (error) {
      console.error(`Error creating alert for project ${projectId}:`, error);
      this.handleError(error, `Failed to create alert for project ${projectId}`);
    }
  }

  async fetchAlerts(credentials: Credentials, projects: Project[]): Promise<MapAlert[]> {
    const alerts: MapAlert[] = [];
    const errors: string[] = [];

    for (const project of projects) {
      try {
        const apiClient = this.getApiClient(credentials);
        const response = await apiClient.get(`/projects/${project.projectId}/remoteDetectionAlerts`);
        const data = response.data;
        const projectAlerts = Array.isArray(data) ? data : data.alerts || [];

        projectAlerts.forEach((alert: any) => {
          if (alert.geometry && alert.geometry.coordinates) {
            alerts.push({
              id: alert.id || `${project.projectId}-${Date.now()}`,
              name: alert.metadata?.alert_type || 'Alert',
              coordinates: alert.geometry.coordinates,
              projectName: project.name,
              detectionDateStart: alert.detectionDateStart || '',
              detectionDateEnd: alert.detectionDateEnd || '',
              sourceId: alert.sourceId || ''
            });
          }
        });
      } catch (error) {
        const errorMessage = `Error fetching alerts for project ${project.name}: ${error instanceof Error ? error.message : 'Unknown error'}`;
        errors.push(errorMessage);
        console.error(errorMessage);
      }
    }

    // Throw error if we couldn't fetch any alerts and there were errors
    if (alerts.length === 0 && errors.length > 0) {
      throw new Error(`Failed to fetch alerts from all projects:\n${errors.join('\n')}`);
    }

    // Log errors but don't throw if we got some alerts
    if (errors.length > 0) {
      console.warn('Some projects failed to load alerts:', errors);
    }

    return alerts;
  }
}

export const apiService = new ApiService();
